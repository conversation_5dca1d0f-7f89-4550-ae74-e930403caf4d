/* SEO Optimized Styles for As A Fractions */

/* Performance Optimizations */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

/* Typography for Better Readability */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.5em;
    color: #2c3e50;
}

h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

h2 {
    font-size: 2rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

h3 {
    font-size: 1.5rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

p {
    margin-bottom: 1rem;
    font-size: 1rem;
    line-height: 1.7;
}

/* Accessibility Improvements */
a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover, a:focus {
    color: #0056b3;
    text-decoration: underline;
}

a:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Skip to main content for screen readers */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* Header Optimizations */
.site-header {
    background-color: #007bff;
    padding: 15px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: #fff !important;
}

.nav-link {
    color: #fff !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: #f8f9fa !important;
    background-color: rgba(255,255,255,0.1);
    border-radius: 4px;
}

/* Main Content Area */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Calculator Section */
.calculator-section {
    background: #fff;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 2rem 0;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
}

/* Alert Styles */
.alert {
    border-radius: 6px;
    padding: 1rem;
    margin: 1rem 0;
    border: none;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

/* Chart Section */
.chart-section {
    background: #fff;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 2rem 0;
    text-align: center;
}

#percentageResult {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
    font-weight: 500;
}

/* Content Sections */
.explanation-section {
    background: #fff;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 2rem 0;
}

.explanation-how {
    background: #fff;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 2rem 0;
    text-align: center;
}

/* Card Styles */
.card-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
    border-bottom: 2px solid #007bff;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
}

.card-header h3 {
    color: #007bff;
    margin: 0;
    font-size: 1.25rem;
}

/* Table Styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

/* Footer */
.site-footer {
    background-color: #343a40;
    color: #f8f9fa;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.site-footer h5 {
    color: #fff;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.site-footer a {
    color: #adb5bd;
    transition: color 0.3s ease;
}

.site-footer a:hover {
    color: #fff;
}

.site-footer .list-unstyled {
    padding-left: 0;
}

.site-footer .list-unstyled li {
    margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .container {
        padding: 0 10px;
    }
    
    .calculator-section,
    .chart-section,
    .explanation-section,
    .explanation-how {
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .card-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .card {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 1.75rem;
    }
    
    .btn-primary {
        width: 100%;
        margin-top: 0.5rem;
    }
    
    .form-inline .form-group {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .form-control {
        width: 100%;
    }
}

/* Print Styles */
@media print {
    .site-header,
    .site-footer,
    .btn,
    .chart-section {
        display: none;
    }
    
    body {
        background: #fff;
        color: #000;
        font-size: 12pt;
    }
    
    h1, h2, h3 {
        page-break-after: avoid;
    }
    
    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #007bff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Focus Indicators for Better Accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }
    
    .btn-primary {
        border: 2px solid #000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}

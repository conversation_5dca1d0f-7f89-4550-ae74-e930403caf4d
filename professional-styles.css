/* Professional Styles for As A Fractions - Modern & Responsive Design */

/* CSS Variables for Consistent Theming */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --background-primary: #ffffff;
    --background-secondary: #f9fafb;
    --background-accent: #f3f4f6;
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-secondary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.25;
    margin-bottom: 0.5em;
    color: var(--text-primary);
}

h1 {
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

h2 {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    margin-bottom: 1rem;
}

h3 {
    font-size: clamp(1.25rem, 3vw, 1.875rem);
    margin-bottom: 0.75rem;
}

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
    line-height: 1.7;
}

.lead {
    font-size: 1.25rem;
    font-weight: 400;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Links */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: 2px;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .container {
        padding: 0 1.5rem;
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 2rem;
    }
}

/* Header Styles */
.site-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.navbar {
    padding: 1rem 0;
}

.navbar-brand {
    font-size: 1.75rem;
    font-weight: 800;
    color: white !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-brand::before {
    content: "🔢";
    font-size: 1.5rem;
}

.navbar-nav {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white !important;
}

/* Mobile Menu */
.navbar-toggler {
    border: none;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-accent) 100%);
    padding: 4rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f3f4f6' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
}

.hero-content {
    position: relative;
    z-index: 1;
}

/* Cards */
.card {
    background: var(--background-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.card-body {
    padding: 2rem;
}

.card-header {
    padding: 1.5rem 2rem 0;
    border-bottom: none;
    background: transparent;
}

.card-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    border: none;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    font-size: 1rem;
    line-height: 1.5;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-secondary {
    background: var(--background-primary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--background-accent);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-success {
    background: linear-gradient(135deg, var(--secondary-color), #059669);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--accent-color), #d97706);
    color: white;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Form Elements */
.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--background-primary);
    color: var(--text-primary);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control::placeholder {
    color: var(--text-light);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Calculator Section */
.calculator-section {
    background: var(--background-primary);
    padding: 3rem 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
}

.calculator-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.calculator-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 500px;
    margin: 0 auto;
}

@media (min-width: 768px) {
    .calculator-form {
        flex-direction: row;
        align-items: end;
    }
}

.calculator-input {
    flex: 1;
}

.calculator-button {
    white-space: nowrap;
}

/* Results */
.result-display {
    background: linear-gradient(135deg, var(--secondary-color), #059669);
    color: white;
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    margin: 1.5rem 0;
    text-align: center;
    box-shadow: var(--shadow-md);
}

.result-display h3 {
    color: white;
    margin-bottom: 0.5rem;
}

.result-fraction {
    font-size: 2rem;
    font-weight: 800;
    margin: 1rem 0;
}

/* Chart Section */
.chart-section {
    background: var(--background-primary);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    margin: 2rem 0;
}

.chart-container {
    max-width: 400px;
    margin: 0 auto;
}

/* Grid Layouts */
.grid {
    display: grid;
    gap: 1.5rem;
}

.grid-cols-1 {
    grid-template-columns: 1fr;
}

@media (min-width: 768px) {
    .grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
    }

    .grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1024px) {
    .grid-cols-4 {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Utilities */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }
.mb-6 { margin-bottom: 1.5rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }
.mt-6 { margin-top: 1.5rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.p-6 { padding: 1.5rem; }

/* Footer Styles */
.site-footer {
    background: linear-gradient(135deg, var(--text-primary) 0%, #111827 100%);
    color: white;
    padding: 4rem 0 2rem;
    margin-top: 4rem;
    position: relative;
    overflow: hidden;
}

.site-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23374151' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-11.046-8.954-20-20-20v20h20z'/%3E%3C/g%3E%3C/svg%3E");
}

.footer-content {
    position: relative;
    z-index: 1;
}

.footer-section {
    margin-bottom: 2rem;
}

.footer-title {
    color: white;
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 3rem;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 1px;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-links a:hover {
    color: white;
    transform: translateX(4px);
}

.footer-links a::before {
    content: '→';
    opacity: 0;
    transition: var(--transition);
}

.footer-links a:hover::before {
    opacity: 1;
}

.footer-description {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    margin-top: 3rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

.footer-social {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
    color: white;
}

/* Breadcrumb */
.breadcrumb {
    background: var(--background-primary);
    padding: 1rem 0;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
}

.breadcrumb-list {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    color: var(--text-light);
}

.breadcrumb-link {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-link:hover {
    color: var(--primary-color);
}

.breadcrumb-current {
    color: var(--text-primary);
    font-weight: 500;
}

/* Table Styles */
.table-container {
    background: var(--background-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    margin: 2rem 0;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-light);
}

.table th {
    background: var(--background-accent);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr:hover {
    background: var(--background-accent);
}

.table-striped tbody tr:nth-child(even) {
    background: rgba(0, 0, 0, 0.02);
}

/* Alert Styles */
.alert {
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    margin: 1rem 0;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: #065f46;
    border-color: rgba(16, 185, 129, 0.2);
}

.alert-info {
    background: rgba(37, 99, 235, 0.1);
    color: #1e3a8a;
    border-color: rgba(37, 99, 235, 0.2);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #92400e;
    border-color: rgba(245, 158, 11, 0.2);
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    color: #991b1b;
    border-color: rgba(239, 68, 68, 0.2);
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1.5rem;
    height: 1.5rem;
    margin: -0.75rem 0 0 -0.75rem;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, var(--background-accent) 25%, var(--border-light) 50%, var(--background-accent) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--border-radius);
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(1rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-2rem);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(2rem);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    .hero-section {
        padding: 2rem 0;
    }

    .calculator-section {
        padding: 2rem 1rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .site-footer {
        padding: 3rem 0 1.5rem;
    }

    .footer-section {
        margin-bottom: 1.5rem;
    }

    .navbar-nav {
        flex-direction: column;
        gap: 0;
        width: 100%;
        margin-top: 1rem;
    }

    .nav-link {
        padding: 0.75rem 0 !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .calculator-form {
        gap: 1rem;
    }

    .result-fraction {
        font-size: 1.5rem;
    }
}

/* Print Styles */
@media print {
    .site-header,
    .site-footer,
    .btn,
    .chart-section {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
        font-size: 12pt;
    }

    .card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }

    h1, h2, h3 {
        page-break-after: avoid;
        color: black !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --text-primary: #000000;
        --text-secondary: #000000;
        --background-primary: #ffffff;
        --border-color: #000000;
    }

    .card {
        border: 2px solid black !important;
    }

    .btn-primary {
        background: #0000ff !important;
        border: 2px solid #000000 !important;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    html {
        scroll-behavior: auto !important;
    }
}

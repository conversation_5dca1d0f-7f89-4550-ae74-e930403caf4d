<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Convert decimals to fractions instantly with As A Fraction. Use our easy-to-use calculator to convert 0.5 into a fraction.">
    <title>Decimals As A Fractions Table</title>
    <!-- Bootstrap CSS -->
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <!-- SEO Meta Tags -->
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://asafractions.com/fractions-table.html">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
        }
        header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
        }
        header p.lead {
            font-size: 1.25rem;
            color: #666;
        }
        .calculator-section form {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .chart-section {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        #fractionChart {
            margin: auto;
        }
        .alert {
            position: relative;
            padding: 0.75rem 3.25rem;
            margin-bottom: 1rem;
            border: 11px solid transparent;
            border-radius: 0.25rem;
        }
        #percentageResult {
            font-size: 1.1rem;
            background-color: #f3eba8;
            border-radius: 10px;
            padding: 8px;
        }
		

    </style>
    <style>
        .explanation-how {
            text-align: center;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .explanation-how h2 {
            font-size: 2rem;
            margin-bottom: 15px;
            color: #333;
        }
        .explanation-how p {
            font-size: 1rem;
            color: #555;
        }
        .explanation-section {
            padding: 2rem;
            background-color: #f9f9f9;
        }
        .card-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
        }
        .card {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            padding: 1.5rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.2);
        }
        .card-header {
            border-bottom: 2px solid #007bff;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
        }
        .card-header h3 {
            margin: 0;
            color: #007bff;
        }
        .card-body p {
            margin: 0.5rem 0;
            color: #333;
        }
        .card-body ul {
            list-style-type: disc;
            margin-left: 1.5rem;
            padding: 0;
        }
        .card-body table {
            width: 100%;
            border-collapse: collapse;
        }
        .card-body table th, .card-body table td {
            border: 1px solid #ddd;
            padding: 0.75rem;
            text-align: left;
        }
        .card-body table th {
            background-color: #f4f4f4;
        }
        @media (max-width: 768px) {
            .card-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        @media (max-width: 480px) {
            .card-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <style>
        .site-header {
            background-color: #007bff;
            padding: 15px 0;
        }
        .site-header .navbar-brand {
            color: #fff;
            font-size: 1.5rem;
            font-weight: bold;
        }
        .site-header .nav-link {
            color: #fff;
            margin-right: 15px;
        }
        .site-header .nav-link:hover {
            color: #f8f9fa;
        }
        .navbar-light .navbar-nav .nav-link {
            color: rgb(255 255 255);
        }
        .navbar-light .navbar-nav .active>.nav-link, .navbar-light .navbar-nav .nav-link.active, .navbar-light .navbar-nav .nav-link.show, .navbar-light .navbar-nav .show>.nav-link {
            color: rgb(150 255 0 / 90%);
            font-weight: 700;
        }
        .site-footer {
            background-color: #343a40;
            color: #f8f9fa;
            padding: 40px 0;
        }
        .site-footer h5 {
            color: #ffffff;
            font-size: 1.2rem;
            margin-bottom: 20px;
        }
        .site-footer p, .site-footer a {
            color: #f8f9fa;
        }
        .site-footer a:hover {
            text-decoration: underline;
        }
        .site-footer .social-icons {
            margin-top: 15px;
        }
        .site-footer .social-icons img {
            width: 24px;
            margin-right: 10px;
        }
        .btn-primary {
            margin: 5px;
        }
        .pagination {
            justify-content: center;
        }
    </style>
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png"><link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png"><link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png"><link rel="manifest" href="/site.webmanifest"><!-- Google tag (gtag.js) --><script async src="https://www.googletagmanager.com/gtag/js?id=G-0XHBW2YCCC"></script><script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'G-0XHBW2YCCC'); </script></head>
<body>
<!-- Header -->
<header class="site-header">
    <div class="container">
        <nav class="navbar navbar-expand-lg navbar-light">
            <a class="navbar-brand" href="/">As A Fractions</a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
				    <li class="nav-item">
                        <a class="nav-link" href="decimals-to-fractions.html">Convert Decimals Manually</a>
                    </li>
										<li class="nav-item">
                        <a class="nav-link" href="measurement-converter.html">Measurement Converter</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about-as-a-fractions.html">About</a>
                    </li>
                </ul>
            </div>
        </nav>
    </div>
</header>

<div class="container mt-5">
    <!-- Header Section -->
    <div class="text-center mb-4">
        <h1 id="pageTitle">Decimals As A Fractions Table</h1>
        <p class="lead">Click on any decimal to learn how to convert that decimal into a fraction.</p>
    </div>

    <!-- Table Section -->
    <section class="explanation-how mt-4">    
        <div class="container mt-5">
            <div id="fractionList" class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-3">
                <!-- Data will be loaded here -->
            </div>
            <div class="text-center mt-4">
                <nav>
                    <ul id="pagination" class="pagination">
                        <!-- Pagination buttons will be added here -->
                    </ul>
                </nav>
            </div>
        </div>
    </section>
    
<!-- Explanation Section -->	
<section class="explanation-section mt-4">
    <h2>Comprehensive Guide to Decimal to Fraction Conversion</h2>
    			<br />

    <!-- Card Container -->
    <div class="card-container">
        <!-- Card 1: Introduction -->
        <div class="card">
            <div class="card-header">
                <h3>Introduction to Decimal and Fraction Conversion</h3>
            </div>
            <div class="card-body">
                <p>Understanding how to convert decimals into fractions is crucial for various mathematical and real-world applications. This guide covers the fundamental concepts, steps, and benefits of converting decimal numbers to fractions.</p>
            </div>
        </div>

        <!-- Card 2: Why Convert Decimals to Fractions -->
        <div class="card">
            <div class="card-header">
                <h3>Why Convert Decimals to Fractions?</h3>
            </div>
            <div class="card-body">
                <ul>
                    <li><strong>Clarity in Mathematical Problems:</strong> Fractions can provide clearer solutions in algebra and other math problems.</li>
                    <li><strong>Practical Applications:</strong> Useful in measurements, recipes, and financial calculations.</li>
                    <li><strong>Educational Benefits:</strong> Helps students understand mathematical concepts better.</li>
                </ul>
            </div>
        </div>

        <!-- Card 3: How It Works -->
        <div class="card">
            <div class="card-header">
                <h3>How Decimal to Fraction Conversion Works</h3>
            </div>
            <div class="card-body">
                <p>The conversion process involves transforming a decimal number into a fraction where the decimal is the numerator and a power of ten is the denominator. Here’s a basic breakdown:</p>
                <ul>
                    <li><strong>Step 1:</strong> Identify the decimal.</li>
                    <li><strong>Step 2:</strong> Convert the decimal to a fraction with 10, 100, or 1000 as the denominator.</li>
                    <li><strong>Step 3:</strong> Simplify the fraction if possible.</li>
                </ul>
            </div>
        </div>

        <!-- Card 4: Conversion Examples -->
        <div class="card">
            <div class="card-header">
                <h3>Examples of Decimal to Fraction Conversion</h3>
            </div>
            <div class="card-body">
                <table>
                    <thead>
                        <tr>
                            <th>Decimal</th>
                            <th>Fraction</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>0.5</td>
                            <td>1/2</td>
                        </tr>
                        <tr>
                            <td>0.75</td>
                            <td>3/4</td>
                        </tr>
                        <tr>
                            <td>0.2</td>
                            <td>1/5</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Card 5: Tools for Conversion -->
        <div class="card">
            <div class="card-header">
                <h3>Tools and Calculators for Decimal to Fraction Conversion</h3>
            </div>
            <div class="card-body">
                <ul>
                    <li><strong>Online Calculators:</strong> Quick and easy tools for immediate conversion.</li>
                    <li><strong>Mathematical Software:</strong> Advanced tools for detailed calculations.</li>
                    <li><strong>Manual Methods:</strong> Techniques for converting decimals by hand.</li>
                </ul>
            </div>
        </div>

        <!-- Card 6: Advantages of Using a Decimal to Fraction Calculator -->
        <div class="card">
            <div class="card-header">
                <h3>Advantages of Using a Decimal to Fraction Calculator</h3>
            </div>
            <div class="card-body">
                <ul>
                    <li><strong>Accuracy:</strong> Ensures precise results every time.</li>
                    <li><strong>Speed:</strong> Provides instant conversions.</li>
                    <li><strong>User-Friendly:</strong> Easy to use with minimal input required.</li>
                </ul>
            </div>
        </div>

        <!-- Card 7: Common Mistakes to Avoid -->
        <div class="card">
            <div class="card-header">
                <h3>Common Mistakes to Avoid in Fraction Conversion</h3>
            </div>
            <div class="card-body">
                <ul>
                    <li><strong>Incorrect Denominator:</strong> Using the wrong power of ten.</li>
                    <li><strong>Failure to Simplify:</strong> Not reducing the fraction to its simplest form.</li>
                    <li><strong>Ignoring Decimal Places:</strong> Misplacing decimal points in conversion.</li>
                </ul>
            </div>
        </div>

        <!-- Card 8: Advanced Conversion Techniques -->
        <div class="card">
            <div class="card-header">
                <h3>Advanced Conversion Techniques</h3>
            </div>
            <div class="card-body">
                <p>For more complex decimals, advanced techniques such as converting repeating decimals or decimals with multiple decimal places can be used:</p>
                <ul>
                    <li><strong>Repeating Decimals:</strong> Use algebraic methods to find the fraction representation.</li>
                    <li><strong>Decimals with Multiple Places:</strong> Convert to fractions with larger denominators and simplify.</li>
                </ul>
            </div>
        </div>

        <!-- Card 9: Educational Resources for Learning Conversion -->
        <div class="card">
            <div class="card-header">
                <h3>Educational Resources for Learning Decimal to Fraction Conversion</h3>
            </div>
            <div class="card-body">
                <ul>
                    <li><strong>Mathematics Textbooks:</strong> Comprehensive guides on fraction conversion.</li>
                    <li><strong>Online Tutorials:</strong> Step-by-step video tutorials.</li>
                    <li><strong>Math Websites:</strong> Interactive exercises and quizzes.</li>
                </ul>
            </div>
        </div>


    </div>
	
	        <!-- Card 10: Conclusion -->
			<br />
        <div >
            <div >
                <h3>Conclusion</h3>
            </div>
            <div >
                <p>Converting decimals to fractions is an essential skill in mathematics with wide-ranging applications. By using our detailed guide and tools, you can achieve accurate conversions and deepen your understanding of numerical relationships.</p>
            </div>
        </div>
	
</section>


    </div>

	<!-- Footer -->
<footer class="site-footer">
    <div class="container">
        <div class="row">
            <div class="col-md-4">
                <h5>About Us</h5>
                <p>As A Fractions offers high-quality tools and services to help you with conversions, calculations, and more. Our mission is to simplify complex processes.</p>
            </div>
            <div class="col-md-4">
                <h5>Quick Links</h5>
                <ul class="list-unstyled">
                    <li><a href="about-as-a-fractions.html">About</a></li>
                    <li><a href="privacy-policy.html">Privacy policy</a></li>
                    <li><a href="terms-of-use.html">Terms of Use</a></li>
                </ul>
            </div>
            <div class="col-md-4">
                <h5>Useful Links</h5>
                <ul class="list-unstyled">
                    <li><a href="decimals-to-fractions.html">Convert Decimals Manually</a></li>
					<li><a href="fractions-table.html">As A Fractions Table</a></li>
					<li><a href="measurement-converter.html">Measurement Converter</a></li>
                </ul>
            </div>
        </div>
        <div class="text-center mt-4">
            <p>&copy; 2024 As A Fractions. All Rights Reserved.</p>
        </div>
    </div>
</footer>

<!-- jQuery and Bootstrap JS -->
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
<!-- Custom JavaScript -->
<script>
    const data = [
{ decimal: '3125' },
{ decimal: '4375' },
{ decimal: '5625' },
{ decimal: '6875' },
{ decimal: '8125' },
{ decimal: '9375' },
{ decimal: '16666' },
{ decimal: '.0625' },
{ decimal: '.08' },
{ decimal: '.125' },
{ decimal: '.15' },
{ decimal: '.16' },
{ decimal: '.1875' },
{ decimal: '.2' },
{ decimal: '.25' },
{ decimal: '.3' },
{ decimal: '.3125' },
{ decimal: '.35' },
{ decimal: '.375' },
{ decimal: '.4' },
{ decimal: '.4375' },
{ decimal: '.5625' },
{ decimal: '.6' },
{ decimal: '.625' },
{ decimal: '.65' },
{ decimal: '.6875' },
{ decimal: '.75' },
{ decimal: '.8' },
{ decimal: '.8125' },
{ decimal: '.83' },
{ decimal: '.875' },
{ decimal: '.9375' },
{ decimal: '0.02' },
{ decimal: '0.04' },
{ decimal: '0.05' },
{ decimal: '0.06' },
{ decimal: '0.0625' },
{ decimal: '0.08' },
{ decimal: '0.1' },
{ decimal: '0.12' },
{ decimal: '0.125' },
{ decimal: '0.15' },
{ decimal: '0.16' },
{ decimal: '0.1875' },
{ decimal: '0.2' },
{ decimal: '0.25' },
{ decimal: '0.27' },
{ decimal: '0.3' },
{ decimal: '0.33' },
{ decimal: '0.35' },
{ decimal: '0.375' },
{ decimal: '0.4' },
{ decimal: '0.45' },
{ decimal: '0.5' },
{ decimal: '0.6' },
{ decimal: '0.625' },
{ decimal: '0.65' },
{ decimal: '0.7' },
{ decimal: '0.75' },
{ decimal: '0.8' },
{ decimal: '0.83' },
{ decimal: '0.875' },
{ decimal: '0.9' },
{ decimal: '1.125' },
{ decimal: '1.2' },
{ decimal: '1.25' },
{ decimal: '1.3' },
{ decimal: '1.33333' },
{ decimal: '1.375' },
{ decimal: '1.4' },
{ decimal: '1.5' },
{ decimal: '1.6' },
{ decimal: '1.75' },
{ decimal: '1.8' },
{ decimal: '1.875' },
{ decimal: '12.5' },
{ decimal: '2.25' },
{ decimal: '2.3' },
{ decimal: '2.4' },
{ decimal: '2.5' },
{ decimal: '2.6' },
{ decimal: '2.75' },
{ decimal: '3.2' },
{ decimal: '3.25' },
{ decimal: '3.5' },
{ decimal: '3.6' },
{ decimal: '3.75' },
{ decimal: '4.5' },
{ decimal: '4.8' },
{ decimal: '5.5' },
{ decimal: '6.25' },
{ decimal: '7.5' },
{ decimal: '0.36' },
{ decimal: '0.55' },
{ decimal: '0.3125' },
{ decimal: '0.8125' },
{ decimal: '0.4375' },
{ decimal: '0.6875' },
{ decimal: '0.5625' },
{ decimal: '0.9375' },
{ decimal: '1.625' },
{ decimal: '5.25' },
{ decimal: '0.66' },
{ decimal: '0.85' },
{ decimal: '0.03125' },
{ decimal: '0.33333' },
{ decimal: '0.83333' },
{ decimal: '2.625' },
{ decimal: '270.75' },
{ decimal: '3.375' },
{ decimal: '6.75' },
{ decimal: '0.126' },
{ decimal: '0.325' },
{ decimal: '0.63' },
{ decimal: '0.67' },
{ decimal: '0.11111' },
{ decimal: '1.33' },
{ decimal: '2.875' },
{ decimal: '3.125' },
{ decimal: '4.25' },
{ decimal: '0.13' },
{ decimal: '0.16666' },
{ decimal: '0.18' },
{ decimal: '0.38' },
{ decimal: '0.88' },
{ decimal: '0.075' },
{ decimal: '0.333' },
{ decimal: '0.667' },
{ decimal: '18.75' },
{ decimal: '2.375' },
{ decimal: '37.5' },
{ decimal: '4.375' },
{ decimal: '4.75' },
{ decimal: '6.7083333' },
{ decimal: '8.75' },
{ decimal: '0.025' },
{ decimal: '0.64' },
{ decimal: '0.015625' },
{ decimal: '0.083' },
{ decimal: '1.333' },
{ decimal: '1.66666666667' },
{ decimal: '1.67' },
{ decimal: '2.125' },
{ decimal: '2.33333' },
{ decimal: '5.625' },
{ decimal: '5.75' },
{ decimal: '8.25' },
{ decimal: '0.03' },
{ decimal: '0.09' },
{ decimal: '0.14' },
{ decimal: '0.17' },
{ decimal: '0.175' },
{ decimal: '0.24' },
{ decimal: '0.28' },
{ decimal: '0.31' },
{ decimal: '0.315' },
{ decimal: '0.32' },
{ decimal: '0.34' },
{ decimal: '0.39' },
{ decimal: '0.40' },
{ decimal: '0.42' },
{ decimal: '0.62' },
{ decimal: '0.675' },
{ decimal: '0.68' },
{ decimal: '0.86' },
{ decimal: '0.09375' },
{ decimal: '0.16666666666' },
{ decimal: '0.66666' },
{ decimal: '1.16' },
{ decimal: '1.35' },
{ decimal: '1.66' },
{ decimal: '1.66667' },
{ decimal: '12.25' },
{ decimal: '3.33' },
{ decimal: '3.625' },
{ decimal: '3.875' },
{ decimal: '4.875' },
{ decimal: '7.25' },
{ decimal: '0.01' },
{ decimal: '0.07' },
{ decimal: '0.19' },
{ decimal: '0.20' },
{ decimal: '0.56' },
{ decimal: '0.58' },
{ decimal: '0.72' },
{ decimal: '0.78' },
{ decimal: '0.825' },
{ decimal: '0.84' },
{ decimal: '0.95' },
{ decimal: '0.22222' },
{ decimal: '0.638876565' },
{ decimal: '1.33333333333' },
{ decimal: '1.5625' },
{ decimal: '1.66666' },
{ decimal: '1.667' },
{ decimal: '1.83' },
{ decimal: '11.25' },
{ decimal: '2.67' },
{ decimal: '20.25' },
{ decimal: '22.5' },
{ decimal: '4.125' },
{ decimal: '62.5' },
{ decimal: '7.75' },
{ decimal: '7.875' },
{ decimal: '0.063' },
{ decimal: '0.10' },
{ decimal: '0.15625' },
{ decimal: '0.167' },
{ decimal: '0.23' },
{ decimal: '0.37' },
{ decimal: '0.44' },
{ decimal: '0.60' },
{ decimal: '0.80' },
{ decimal: '0.08333' },
{ decimal: '0.225' },
{ decimal: '0.275' },
{ decimal: '0.44444' },
{ decimal: '1.65' },
{ decimal: '2.33' },
{ decimal: '2.66' },
{ decimal: '3.14' },
{ decimal: '5.33333' },
{ decimal: '5.875' },
{ decimal: '6.875' },
{ decimal: '9.75' },
{ decimal: '0.008' },
{ decimal: '0.11' },
{ decimal: '0.21875' },
{ decimal: '0.43' },
{ decimal: '0.46' },
{ decimal: '0.54' },
{ decimal: '0.69' },
{ decimal: '0.81' },
{ decimal: '0.833' },
{ decimal: '0.87' },
{ decimal: '0.96' },
{ decimal: '0.0125' },
{ decimal: '0.064' },
{ decimal: '0.425' },
{ decimal: '0.525' },
{ decimal: '0.666' },
{ decimal: '0.83333333333' },
{ decimal: '0.94' },
{ decimal: '1.18' },
{ decimal: '1.26' },
{ decimal: '1.44' },
{ decimal: '1.55' },
{ decimal: '1.6875' },
{ decimal: '16.5' },
{ decimal: '2.16' },
{ decimal: '2.35' },
{ decimal: '3.33333' },
{ decimal: '4.625' },
{ decimal: '6.125' },
{ decimal: '6.375' },
{ decimal: '87.5' },
{ decimal: '9.25' },
{ decimal: '9.375' },
{ decimal: '0.005' },
{ decimal: '0.045' },
{ decimal: '0.065' },
{ decimal: '0.093' },
{ decimal: '0.157' },
{ decimal: '0.185' },
{ decimal: '0.187' },
{ decimal: '0.197' },
{ decimal: '0.21' },
{ decimal: '0.22' },
{ decimal: '0.26' },
{ decimal: '0.28125' },
{ decimal: '0.3' },
{ decimal: '0.34375' },
{ decimal: '0.41' },
{ decimal: '0.48' },
{ decimal: '0.57' },
{ decimal: '0.65625' },
{ decimal: '0.71' },
{ decimal: '0.71875' },
{ decimal: '0.78125' },
{ decimal: '0.82' },
{ decimal: '0.89' },
{ decimal: '0.0016' },
{ decimal: '0.007' },
{ decimal: '0.009' },
{ decimal: '0.012' },
{ decimal: '0.016' },
{ decimal: '0.035' },
{ decimal: '0.111' },
{ decimal: '0.6666' },
{ decimal: '0.725' },
{ decimal: '1.08' },
{ decimal: '1.28' },
{ decimal: '1.3333' },
{ decimal: '1.38' },
{ decimal: '2.333' },
{ decimal: '2.45' },
{ decimal: '2.66666666667' },
{ decimal: '3.15' },
{ decimal: '3.333' },
{ decimal: '5.0625' },
{ decimal: '8.125' },
{ decimal: '0.67777' },
{ decimal: '2.2' },
{ decimal: '6.5' },
{ decimal: '8.0' },
{ decimal: '0.52' },
{ decimal: '1.7' },
{ decimal: '2.8' },
{ decimal: '3.00' },
{ decimal: '3.3' },
{ decimal: '5.0' },
{ decimal: '1.1' },
{ decimal: '1.117' },
{ decimal: '10.5' },
{ decimal: '2.9' },
{ decimal: '3.4' },
{ decimal: '3.8' },
{ decimal: '4.2' },
{ decimal: '4.6' },
{ decimal: '5.6' },
{ decimal: '6.264' },
{ decimal: '8.5' },
{ decimal: '1' },
{ decimal: '2' },
{ decimal: '3' },
{ decimal: '4' },
{ decimal: '5' },
{ decimal: '6' },
{ decimal: '7' },
{ decimal: '8' },
{ decimal: '12' },
{ decimal: '15' },
{ decimal: '16' },
{ decimal: '20' },
{ decimal: '25' },
{ decimal: '33' },
{ decimal: '35' },
{ decimal: '40' },
{ decimal: '45' },
{ decimal: '50' },
{ decimal: '60' },
{ decimal: '65' },
{ decimal: '75' },
{ decimal: '125' },
{ decimal: '315' },
{ decimal: '375' },
{ decimal: '625' },
{ decimal: '675' },
{ decimal: '825' },
{ decimal: '875' }

        // Add more data as needed
    ];

    const itemsPerPage = 200; // Number of items per page
    let currentPage = 1;

    function renderTable(data, page) {
        const start = (page - 1) * itemsPerPage;
        const end = start + itemsPerPage;
        const paginatedData = data.slice(start, end);

        const list = document.getElementById('fractionList');
        list.innerHTML = '';

        paginatedData.forEach(item => {
            const div = document.createElement('div');
            div.className = 'col';
            div.innerHTML = `

					
			<div class="col"><a href="./?decimal=${item.decimal}" class="btn btn-primary w-100">${item.decimal} as a fraction</a></div>

            `;
            list.appendChild(div);
        });
    }

    function renderPagination(totalItems, currentPage) {
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';

        for (let i = 1; i <= totalPages; i++) {
            const li = document.createElement('li');
            li.className = 'page-item';
            li.innerHTML = `<a class="page-link" href="#">${i}</a>`;
            if (i === currentPage) {
                li.classList.add('active');
            }
            li.addEventListener('click', function(event) {
                event.preventDefault();
                currentPage = i;
                renderTable(data, currentPage);
                renderPagination(data.length, currentPage);
            });
            pagination.appendChild(li);
        }
    }

    // Initial render
    renderTable(data, currentPage);
    renderPagination(data.length, currentPage);
</script>
</body>
</html>

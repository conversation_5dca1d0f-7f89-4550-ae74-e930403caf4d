/**
 * Professional Interactions for As A Fractions
 * Enhanced user experience with smooth animations and modern interactions
 */

class FractionCalculator {
    constructor() {
        this.init();
        this.setupEventListeners();
        this.setupAnimations();
        this.setupPerformanceOptimizations();
    }

    init() {
        // Initialize calculator state
        this.currentDecimal = null;
        this.currentFraction = null;
        this.chart = null;
        
        // Cache DOM elements
        this.elements = {
            form: document.getElementById('fractionForm'),
            input: document.getElementById('decimalInput'),
            result: document.getElementById('result'),
            pageTitle: document.getElementById('pageTitle'),
            percentageResult: document.getElementById('percentageResult'),
            chartCanvas: document.getElementById('fractionChart')
        };

        // Initialize chart if canvas exists
        if (this.elements.chartCanvas) {
            this.initChart();
        }

        // Check for URL parameters
        this.handleURLParameters();
    }

    setupEventListeners() {
        // Form submission
        if (this.elements.form) {
            this.elements.form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCalculation();
            });
        }

        // Real-time input validation
        if (this.elements.input) {
            this.elements.input.addEventListener('input', this.debounce((e) => {
                this.validateInput(e.target.value);
            }, 300));

            this.elements.input.addEventListener('keypress', (e) => {
                this.handleKeyPress(e);
            });
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Mobile menu toggle
        const navbarToggler = document.querySelector('.navbar-toggler');
        const navbarCollapse = document.querySelector('.navbar-collapse');
        
        if (navbarToggler && navbarCollapse) {
            navbarToggler.addEventListener('click', () => {
                navbarCollapse.classList.toggle('show');
            });
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (navbarCollapse && navbarCollapse.classList.contains('show')) {
                if (!navbarToggler.contains(e.target) && !navbarCollapse.contains(e.target)) {
                    navbarCollapse.classList.remove('show');
                }
            }
        });
    }

    setupAnimations() {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.card, .calculator-section, .chart-section').forEach(el => {
            observer.observe(el);
        });

        // Add stagger animation to cards
        document.querySelectorAll('.card').forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });
    }

    setupPerformanceOptimizations() {
        // Lazy load images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('skeleton');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }

        // Preload critical resources
        this.preloadCriticalResources();
    }

    handleURLParameters() {
        const urlParams = new URLSearchParams(window.location.search);
        const decimalParam = urlParams.get('decimal');
        
        if (decimalParam && this.elements.input) {
            this.elements.input.value = decimalParam;
            this.handleCalculation();
        }
    }

    handleCalculation() {
        const inputValue = this.elements.input?.value?.trim();
        
        if (!inputValue) {
            this.showError('Please enter a decimal number');
            return;
        }

        const decimal = parseFloat(inputValue);
        
        if (isNaN(decimal)) {
            this.showError('Please enter a valid decimal number');
            return;
        }

        // Show loading state
        this.showLoading();

        // Simulate processing time for better UX
        setTimeout(() => {
            this.calculateFraction(decimal);
            this.hideLoading();
        }, 300);
    }

    calculateFraction(decimal) {
        try {
            const fraction = this.decimalToFraction(decimal);
            this.currentDecimal = decimal;
            this.currentFraction = fraction;
            
            this.displayResult(decimal, fraction);
            this.updateChart(decimal);
            this.updatePageTitle(decimal, fraction);
            this.updateURL(decimal);
            
            // Track conversion in analytics
            this.trackConversion(decimal, fraction);
            
        } catch (error) {
            this.showError('Error calculating fraction. Please try again.');
            console.error('Calculation error:', error);
        }
    }

    decimalToFraction(decimal) {
        const tolerance = 1.0E-6;
        let h1 = 1, h2 = 0, k1 = 0, k2 = 1;
        let b = decimal;
        
        do {
            const a = Math.floor(b);
            let aux = h1; h1 = a * h1 + h2; h2 = aux;
            aux = k1; k1 = a * k1 + k2; k2 = aux;
            b = 1 / (b - a);
        } while (Math.abs(decimal - h1 / k1) > decimal * tolerance);
        
        return {
            numerator: h1,
            denominator: k1,
            display: `${h1}/${k1}`,
            decimal: h1 / k1
        };
    }

    displayResult(decimal, fraction) {
        if (!this.elements.result) return;

        const resultHTML = `
            <div class="result-display animate-fade-in">
                <h3>Result</h3>
                <p>The decimal <strong>${decimal}</strong> as a fraction is:</p>
                <div class="result-fraction">${fraction.display}</div>
                <p class="text-muted">Simplified to lowest terms</p>
            </div>
        `;

        this.elements.result.innerHTML = resultHTML;
        this.elements.result.classList.remove('d-none');
        
        // Animate result appearance
        this.elements.result.style.opacity = '0';
        this.elements.result.style.transform = 'translateY(20px)';
        
        requestAnimationFrame(() => {
            this.elements.result.style.transition = 'all 0.5s ease-out';
            this.elements.result.style.opacity = '1';
            this.elements.result.style.transform = 'translateY(0)';
        });
    }

    updateChart(decimal) {
        if (!this.chart) return;

        const fractionValue = Math.min(decimal, 1); // Cap at 1 for visualization
        const remainingValue = 1 - fractionValue;

        this.chart.data.datasets[0].data = [fractionValue, remainingValue];
        this.chart.update('active');

        // Update percentage display
        if (this.elements.percentageResult) {
            const percentage = (fractionValue * 100).toFixed(2);
            this.elements.percentageResult.innerHTML = `
                <div class="chart-info animate-fade-in">
                    <p><strong>${percentage}%</strong> of the circle represents the fraction value</p>
                    <p>Remaining: <strong>${(remainingValue * 100).toFixed(2)}%</strong></p>
                </div>
            `;
        }
    }

    initChart() {
        if (!this.elements.chartCanvas || typeof Chart === 'undefined') return;

        const ctx = this.elements.chartCanvas.getContext('2d');
        
        this.chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [0.5, 0.5],
                    backgroundColor: [
                        'rgba(37, 99, 235, 0.8)',
                        'rgba(229, 231, 235, 0.8)'
                    ],
                    borderColor: [
                        'rgba(37, 99, 235, 1)',
                        'rgba(229, 231, 235, 1)'
                    ],
                    borderWidth: 2,
                    hoverOffset: 4
                }],
                labels: ['Fraction Value', 'Remaining']
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 14
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const percentage = (context.parsed * 100).toFixed(2);
                                return `${context.label}: ${percentage}%`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    }

    updatePageTitle(decimal, fraction) {
        if (this.elements.pageTitle) {
            this.elements.pageTitle.textContent = `${decimal} as a Fraction = ${fraction.display}`;
        }

        // Update document title
        document.title = `${decimal} as a Fraction - Convert ${decimal} to Fraction (${fraction.display}) | As A Fractions`;
        
        // Update meta description
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
            metaDescription.setAttribute('content', 
                `Convert ${decimal} to a fraction: ${decimal} = ${fraction.display}. Learn step-by-step how to convert ${decimal} decimal to fraction with our free calculator.`
            );
        }
    }

    updateURL(decimal) {
        const newURL = new URL(window.location);
        newURL.searchParams.set('decimal', decimal);
        window.history.replaceState({}, '', newURL);
    }

    validateInput(value) {
        const input = this.elements.input;
        if (!input) return;

        // Remove invalid characters
        const cleanValue = value.replace(/[^0-9.-]/g, '');
        
        if (cleanValue !== value) {
            input.value = cleanValue;
        }

        // Visual feedback
        if (cleanValue && !isNaN(parseFloat(cleanValue))) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        } else if (cleanValue) {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
        } else {
            input.classList.remove('is-valid', 'is-invalid');
        }
    }

    handleKeyPress(e) {
        // Allow: backspace, delete, tab, escape, enter
        if ([8, 9, 27, 13, 46].indexOf(e.keyCode) !== -1 ||
            // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
            (e.keyCode === 65 && e.ctrlKey === true) ||
            (e.keyCode === 67 && e.ctrlKey === true) ||
            (e.keyCode === 86 && e.ctrlKey === true) ||
            (e.keyCode === 88 && e.ctrlKey === true)) {
            return;
        }
        
        // Ensure that it is a number or decimal point
        if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && 
            (e.keyCode < 96 || e.keyCode > 105) && 
            e.keyCode !== 190 && e.keyCode !== 110) {
            e.preventDefault();
        }
    }

    showLoading() {
        if (this.elements.form) {
            this.elements.form.classList.add('loading');
        }
    }

    hideLoading() {
        if (this.elements.form) {
            this.elements.form.classList.remove('loading');
        }
    }

    showError(message) {
        if (!this.elements.result) return;

        this.elements.result.innerHTML = `
            <div class="alert alert-error animate-fade-in">
                <strong>Error:</strong> ${message}
            </div>
        `;
        this.elements.result.classList.remove('d-none');
    }

    trackConversion(decimal, fraction) {
        // Google Analytics tracking
        if (typeof gtag !== 'undefined') {
            gtag('event', 'fraction_conversion', {
                'decimal_value': decimal,
                'fraction_result': fraction.display,
                'event_category': 'calculator',
                'event_label': 'conversion_completed'
            });
        }
    }

    preloadCriticalResources() {
        // Preload important CSS and JS files
        const resources = [
            { href: 'professional-styles.css', as: 'style' },
            { href: 'https://cdn.jsdelivr.net/npm/chart.js', as: 'script' }
        ];

        resources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource.href;
            link.as = resource.as;
            document.head.appendChild(link);
        });
    }

    // Utility function for debouncing
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new FractionCalculator();
});

// Service Worker registration for PWA
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="Explore our advanced Decimal and Fraction Measurement Converter. Easily convert between decimal numbers and fractions with an intuitive interface and helpful tips on using the tool effectively.">
<meta name="keywords" content="Decimal to Fraction Converter, Fraction to Decimal Converter, Measurement Conversion, Decimal Conversion, Fraction Conversion, Online Calculator">
<meta name="author" content="As A Fractions">
    <title>Measurement Converter</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&family=Cairo:wght@400;700&display=swap" rel="stylesheet">
   

   <style>
        body {
            background-color: #f8f9fa;
        }
        header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
        }
        header p.lead {
            font-size: 1.25rem;
            color: #666;
        }
        .calculator-section form {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .chart-section {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        #fractionChart {
            margin: auto;
        }
        .alert {
            position: relative;
            padding: 0.75rem 3.25rem;
            margin-bottom: 1rem;
            border: 11px solid transparent;
            border-radius: 0.25rem;
        }
        #percentageResult {
            font-size: 1.1rem;
            background-color: #f3eba8;
            border-radius: 10px;
            padding: 8px;
        }
		

    </style>
    <style>
        .explanation-how {
            text-align: center;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .explanation-how h2 {
            font-size: 2rem;
            margin-bottom: 15px;
            color: #333;
        }
        .explanation-how p {
            font-size: 1rem;
            color: #555;
        }
        .explanation-section {
            padding: 2rem;
            background-color: #f9f9f9;
        }
        .card-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
        }
        .card {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            padding: 1.5rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.2);
        }
        .card-header {
            border-bottom: 2px solid #007bff;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
        }
        .card-header h3 {
            margin: 0;
            color: #007bff;
        }
        .card-body p {
            margin: 0.5rem 0;
            color: #333;
        }
        .card-body ul {
            list-style-type: disc;
            margin-left: 1.5rem;
            padding: 0;
        }
        .card-body table {
            width: 100%;
            border-collapse: collapse;
        }
        .card-body table th, .card-body table td {
            border: 1px solid #ddd;
            padding: 0.75rem;
            text-align: left;
        }
        .card-body table th {
            background-color: #f4f4f4;
        }
        @media (max-width: 768px) {
            .card-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        @media (max-width: 480px) {
            .card-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <style>
        .site-header {
            background-color: #007bff;
            padding: 15px 0;
        }
        .site-header .navbar-brand {
            color: #fff;
            font-size: 1.5rem;
            font-weight: bold;
        }
        .site-header .nav-link {
            color: #fff;
            margin-right: 15px;
        }
        .site-header .nav-link:hover {
            color: #f8f9fa;
        }
        .navbar-light .navbar-nav .nav-link {
            color: rgb(255 255 255);
        }
        .navbar-light .navbar-nav .active>.nav-link, .navbar-light .navbar-nav .nav-link.active, .navbar-light .navbar-nav .nav-link.show, .navbar-light .navbar-nav .show>.nav-link {
            color: rgb(150 255 0 / 90%);
            font-weight: 700;
        }
        .site-footer {
            background-color: #343a40;
            color: #f8f9fa;
            padding: 40px 0;
        }
        .site-footer h5 {
            color: #ffffff;
            font-size: 1.2rem;
            margin-bottom: 20px;
        }
        .site-footer p, .site-footer a {
            color: #f8f9fa;
        }
        .site-footer a:hover {
            text-decoration: underline;
        }
        .site-footer .social-icons {
            margin-top: 15px;
        }
        .site-footer .social-icons img {
            width: 24px;
            margin-right: 10px;
        }
        .btn-primary {
            margin: 5px;
        }
        .pagination {
            justify-content: center;
        }
		
		
		        @media (max-width: 768px) {
            .hero-section h2 {
                font-size: 2rem;
            }
            .article h1 {
                font-size: 2rem;
            }
            .article-content h2 {
                font-size: 1.5rem;
            }
        }
		        .article {
            background: #ffffff;
            padding: 2rem;
            margin: 1rem 0;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .article h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #007bff;
        }
        .article p {
            margin: 1rem 0;
            line-height: 1.8;
        }
        .article img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .article-meta {
            font-size: 0.9rem;
            color: #555555;
            margin-bottom: 1.5rem;
        }
        .article-meta span {
            margin-left: 1rem;
        }
        .article-content h2 {
            font-size: 2rem;
            margin-top: 2rem;
        }
        .article-content h3 {
            font-size: 1.5rem;
            margin-top: 1.5rem;
        }
		</style>
		<style>
       .card {
            margin-bottom: 20px;
        }
        .result {
            margin-top: 20px;
        }
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
        }
        .btn-custom {
            background-color: #007bff;
            color: #fff;
        }
        .btn-custom:hover {
            background-color: #0056b3;
        }
    </style>
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png"><link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png"><link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png"><link rel="manifest" href="/site.webmanifest"><!-- Google tag (gtag.js) --><script async src="https://www.googletagmanager.com/gtag/js?id=G-0XHBW2YCCC"></script><script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'G-0XHBW2YCCC'); </script></head>
<body>
<!-- Header -->
<header class="site-header">
    <div class="container">
        <nav class="navbar navbar-expand-lg navbar-light">
            <a class="navbar-brand" href="/">As A Fractions</a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
				    <li class="nav-item">
                        <a class="nav-link" href="decimals-to-fractions.html">Convert Decimals Manually</a>
                    </li>
										<li class="nav-item">
                        <a class="nav-link" href="measurement-converter.html">Measurement Converter</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about-as-a-fractions.html">About</a>
                    </li>
                </ul>
            </div>
        </nav>
    </div>
</header>



    <section class="container my-5">
    <div class="container">
        <h1 class="text-center">Measurement Converter Tool</h1>
		<div class="card">
<p>&nbsp;Welcome to the ultimate <strong>Measurement Converter Tool</strong> – your comprehensive solution for seamlessly converting a wide range of measurements. Whether you're working with decimal and fraction conversions, or handling various units of length, weight, volume, and temperature.</p>
       </div>
	   <!-- Decimal & Fraction Converter -->
	   
	   <div class="card">
	   <div class="card-body">
                        <h2 class="card-title">Decimal-Fraction Converter</h2>
						<p>Easily convert between decimals and fractions with our intuitive calculator. Switch effortlessly between formats for precise mathematical operations.</p>
                        <form id="conversion-form">
                            <div class="mb-3">
                                <label for="inputValue" class="form-label">Enter Value:</label>
                                <input type="text" class="form-control" id="inputValue" placeholder="Enter value here">
                            </div>
                            <div class="mb-3">
                                <label for="conversionType" class="form-label">Select Conversion Type:</label>
                                <select class="form-select" id="conversionType">
                                    <option value="decimalToFraction">Decimal to Fraction</option>
                                    <option value="fractionToDecimal">Fraction to Decimal</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="convert()">Convert</button>
                        </form>

                        <div id="result" class="mt-3">
                            <!-- Conversion result will appear here -->
                        </div>
                    </div>
	    </div> 
        
        <!-- Multi-Measurement Converter -->
        <div class="card">
            <div class="card-body">
                <h3 class="card-title">Multi-Measurement Converter</h3>
				<p>Streamline your measurement conversions with our versatile <strong>Multi-Measurement Converter</strong>. This tool is designed to handle a wide array of units across various categories such as length, weight, volume, and temperature. Effortlessly convert between different units with precision and speed, and ensure you get accurate results for any measurement need. Ideal for professionals, students, and everyday users, this converter simplifies complex conversions and provides a user-friendly interface for managing all your measurement tasks in one place.</p>
                <form id="measurementForm">
                    <div class="form-group">
                        <label for="measurementValue">Value:</label>
                        <input type="number" step="any" class="form-control" id="measurementValue" required>
                    </div>
                    <div class="form-group">
                        <label for="fromUnit">From Unit:</label>
                        <select class="form-control" id="fromUnit" onchange="populateUnitOptions()">
                            <option value="length">Length</option>
                            <option value="weight">Weight</option>
                            <option value="volume">Volume</option>
                            <option value="temperature">Temperature</option>
                        </select>
                    </div>
					                    <div class="form-group">
                        <label for="toUnitType">From Unit Type:</label>
                        <select class="form-control" id="toUnitType">
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="fromUnitType">To Unit Type:</label>
                        <select class="form-control" id="fromUnitType">
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>

                    <button type="button" class="btn btn-custom" onclick="convertMeasurement()">Convert</button>
                    <div id="measurementResult" class="result"></div>
                </form>
            </div>
        </div>
    </div>
    </section>
 
<script>
function convert() {
    const inputValue = document.getElementById('inputValue').value.trim();
    const conversionType = document.getElementById('conversionType').value;
    const resultElement = document.getElementById('result');
    
    if (!inputValue) {
        resultElement.textContent = 'Please enter a value.';
        return;
    }

    if (conversionType === 'decimalToFraction') {
        const decimalValue = parseFloat(inputValue);
        if (isNaN(decimalValue)) {
            resultElement.textContent = 'Invalid decimal value.';
            return;
        }
        const fraction = decimalToFraction(decimalValue);
        resultElement.textContent = `Fraction: ${fraction}`;
    } else if (conversionType === 'fractionToDecimal') {
        const [numerator, denominator] = inputValue.split('/').map(Number);
        if (denominator === 0 || isNaN(numerator) || isNaN(denominator)) {
            resultElement.textContent = 'Invalid fraction value.';
            return;
        }
        const decimal = numerator / denominator;
        resultElement.textContent = `Decimal: ${decimal.toFixed(5)}`;
    }
}

function decimalToFraction(decimal) {
    const precision = 1e-10;
    let numerator = 1;
    let denominator = 1;

    while (Math.abs(decimal - numerator / denominator) > precision) {
        if (decimal > numerator / denominator) {
            numerator++;
        } else {
            denominator++;
        }
    }

    return `${numerator}/${denominator}`;
}

</script>

    <script>

        // Multi-Measurement Conversion
        const units = {
            length: {
                meters: { name: "Meters", conversion: 1 },
                kilometers: { name: "Kilometers", conversion: 0.001 },
                miles: { name: "Miles", conversion: 0.000621371 },
                feet: { name: "Feet", conversion: 3.28084 },
                inches: { name: "Inches", conversion: 39.3701 },
                centimeters: { name: "Centimeters", conversion: 100 },
                millimeters: { name: "Millimeters", conversion: 1000 },
                yards: { name: "Yards", conversion: 1.09361 }
            },
            weight: {
                kilograms: { name: "Kilograms", conversion: 1 },
                grams: { name: "Grams", conversion: 1000 },
                pounds: { name: "Pounds", conversion: 2.20462 },
                ounces: { name: "Ounces", conversion: 35.274 },
                tons: { name: "Tons", conversion: 0.00110231 }
            },
            volume: {
                liters: { name: "Liters", conversion: 1 },
                milliliters: { name: "Milliliters", conversion: 1000 },
                gallons: { name: "Gallons", conversion: 0.264172 },
                cubic_meters: { name: "Cubic Meters", conversion: 0.001 },
                cubic_inches: { name: "Cubic Inches", conversion: 61.0237 }
            },
            temperature: {
                celsius: { name: "Celsius", conversion: 1, offset: 0 },
                fahrenheit: { name: "Fahrenheit", conversion: 1.8, offset: 32 },
                kelvin: { name: "Kelvin", conversion: 1, offset: -273.15 }
            }
        };

        function populateUnitOptions() {
            const fromUnitSelect = document.getElementById('fromUnitType');
            const toUnitSelect = document.getElementById('toUnitType');
            const unitType = document.getElementById('fromUnit').value;

            const unitsList = units[unitType];
            fromUnitSelect.innerHTML = '';
            toUnitSelect.innerHTML = '';

            for (let unit in unitsList) {
                fromUnitSelect.innerHTML += `<option value="${unit}">${unitsList[unit].name}</option>`;
                toUnitSelect.innerHTML += `<option value="${unit}">${unitsList[unit].name}</option>`;
            }
        }

        function convertMeasurement() {
            const value = parseFloat(document.getElementById('measurementValue').value);
            const fromUnit = document.getElementById('fromUnitType').value;
            const toUnit = document.getElementById('toUnitType').value;
            const unitType = document.getElementById('fromUnit').value;

            const fromUnitInfo = units[unitType][fromUnit];
            const toUnitInfo = units[unitType][toUnit];

            let result;

            if (unitType === 'temperature') {
                // Temperature Conversion
                if (fromUnit === 'celsius') {
                    if (toUnit === 'fahrenheit') {
                        result = (value * fromUnitInfo.conversion) + fromUnitInfo.offset;
                    } else if (toUnit === 'kelvin') {
                        result = value - fromUnitInfo.offset;
                    } else {
                        result = value;
                    }
                } else if (fromUnit === 'fahrenheit') {
                    if (toUnit === 'celsius') {
                        result = (value - fromUnitInfo.offset) / fromUnitInfo.conversion;
                    } else if (toUnit === 'kelvin') {
                        result = (value - fromUnitInfo.offset) / fromUnitInfo.conversion + 273.15;
                    } else {
                        result = value;
                    }
                } else if (fromUnit === 'kelvin') {
                    if (toUnit === 'celsius') {
                        result = value - fromUnitInfo.offset;
                    } else if (toUnit === 'fahrenheit') {
                        result = (value - fromUnitInfo.offset) * fromUnitInfo.conversion + 32;
                    } else {
                        result = value;
                    }
                }
            } else {
                // Other Units Conversion
                result = value * (fromUnitInfo.conversion / toUnitInfo.conversion);
            }

            document.getElementById('measurementResult').innerText = `Converted Value: ${result.toFixed(4)}`;

            // Update the chart
            updateChart(value, result);
        }

        // Chart.js setup
        let ctx;
        let chart;

        function updateChart(input, output) {
            if (chart) {
                chart.destroy();
            }
            ctx = document.getElementById('chart').getContext('2d');
            chart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Input', 'Output'],
                    datasets: [{
                        label: 'Measurement Values',
                        data: [input, output],
                        backgroundColor: ['rgba(75, 192, 192, 0.2)', 'rgba(153, 102, 255, 0.2)'],
                        borderColor: ['rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)'],
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Initialize
        populateUnitOptions();
    </script>

	<!-- Footer -->
<footer class="site-footer">
    <div class="container">
        <div class="row">
            <div class="col-md-4">
                <h5>About Us</h5>
                <p>As A Fractions offers high-quality tools and services to help you with conversions, calculations, and more. Our mission is to simplify complex processes.</p>
            </div>
            <div class="col-md-4">
                <h5>Quick Links</h5>
                <ul class="list-unstyled">
                    <li><a href="about-as-a-fractions.html">About</a></li>
                    <li><a href="privacy-policy.html">Privacy policy</a></li>
                    <li><a href="terms-of-use.html">Terms of Use</a></li>
                </ul>
            </div>
            <div class="col-md-4">
                <h5>Useful Links</h5>
                <ul class="list-unstyled">
                    <li><a href="decimals-to-fractions.html">Convert Decimals Manually</a></li>
					<li><a href="fractions-table.html">As A Fractions Table</a></li>
					<li><a href="measurement-converter.html">Measurement Converter</a></li>
                </ul>
            </div>
        </div>
        <div class="text-center mt-4">
            <p>&copy; 2024 As A Fractions. All Rights Reserved.</p>
        </div>
    </div>
</footer>
</body>
</html>

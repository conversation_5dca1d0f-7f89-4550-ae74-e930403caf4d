/**
 * <PERSON><PERSON><PERSON> to apply SEO enhancements to all fraction pages
 * This script should be run to update all existing fraction pages with proper SEO
 */

const fs = require('fs');
const path = require('path');

// Function to convert decimal to fraction
function decimalToFraction(decimal) {
    const num = parseFloat(decimal);
    if (isNaN(num)) return { numerator: 1, denominator: 1, display: '1/1' };
    
    const tolerance = 1.0E-6;
    let h1 = 1, h2 = 0, k1 = 0, k2 = 1;
    let b = num;
    
    do {
        const a = Math.floor(b);
        let aux = h1; h1 = a * h1 + h2; h2 = aux;
        aux = k1; k1 = a * k1 + k2; k2 = aux;
        b = 1 / (b - a);
    } while (Math.abs(num - h1 / k1) > num * tolerance);
    
    return {
        numerator: h1,
        denominator: k1,
        display: `${h1}/${k1}`
    };
}

// Function to generate SEO-optimized HTML for fraction pages
function generateFractionPageHTML(decimal, fraction) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Convert ${decimal} to a fraction: ${decimal} = ${fraction.display}. Learn step-by-step how to convert ${decimal} decimal to fraction with our free calculator and comprehensive guide.">
    <meta name="keywords" content="${decimal} as a fraction, ${decimal} to fraction, convert ${decimal} to fraction, ${decimal} fraction, decimal to fraction ${decimal}">
    <meta name="author" content="As A Fractions">
    <meta name="language" content="en">
    <meta name="revisit-after" content="7 days">
    <title>${decimal} as a Fraction - Convert ${decimal} to Fraction (${fraction.display}) | Step-by-Step Guide</title>
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://asafractions.com/${decimal}-as-a-fraction.html">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="${decimal} as a Fraction - Convert ${decimal} to Fraction (${fraction.display})">
    <meta property="og:description" content="Convert ${decimal} to a fraction: ${decimal} = ${fraction.display}. Learn step-by-step how to convert ${decimal} decimal to fraction with our free calculator.">
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://asafractions.com/${decimal}-as-a-fraction.html">
    <meta property="og:image" content="https://asafractions.com/android-chrome-512x512.png">
    <meta property="og:site_name" content="As A Fractions">
    <meta property="og:locale" content="en_US">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="${decimal} as a Fraction - Convert ${decimal} to Fraction (${fraction.display})">
    <meta name="twitter:description" content="Convert ${decimal} to a fraction: ${decimal} = ${fraction.display}. Learn step-by-step how to convert ${decimal} decimal to fraction with our free calculator.">
    <meta name="twitter:image" content="https://asafractions.com/android-chrome-512x512.png">
    
    <!-- Additional SEO Meta Tags -->
    <meta name="theme-color" content="#007bff">
    <meta name="msapplication-TileColor" content="#007bff">
    <meta name="robots" content="index, follow">
    
    <!-- Bootstrap CSS -->
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    
    <!-- Favicon and Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">

    <!-- JSON-LD Schema Markup -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Article",
        "headline": "${decimal} as a Fraction - Convert ${decimal} to Fraction (${fraction.display})",
        "description": "Convert ${decimal} to a fraction: ${decimal} = ${fraction.display}. Learn step-by-step how to convert ${decimal} decimal to fraction with our free calculator and comprehensive guide.",
        "author": {
            "@type": "Organization",
            "name": "As A Fractions",
            "url": "https://asafractions.com/"
        },
        "publisher": {
            "@type": "Organization",
            "name": "As A Fractions",
            "logo": {
                "@type": "ImageObject",
                "url": "https://asafractions.com/android-chrome-512x512.png"
            }
        },
        "datePublished": "2024-01-01",
        "dateModified": "2024-12-19",
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "https://asafractions.com/${decimal}-as-a-fraction.html"
        },
        "image": "https://asafractions.com/android-chrome-512x512.png",
        "articleSection": "Mathematics",
        "keywords": ["${decimal} as a fraction", "${decimal} to fraction", "convert ${decimal} to fraction", "decimal to fraction"],
        "about": {
            "@type": "Thing",
            "name": "Decimal to Fraction Conversion",
            "description": "Mathematical process of converting ${decimal} decimal to its fraction equivalent ${fraction.display}"
        }
    }
    </script>

    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "MathSolver",
        "name": "${decimal} to Fraction Converter",
        "description": "Convert ${decimal} decimal to fraction ${fraction.display}",
        "mathExpression": "${decimal} = ${fraction.display}",
        "educationalLevel": "beginner",
        "learningResourceType": "calculator",
        "about": {
            "@type": "Thing",
            "name": "Fraction Conversion",
            "description": "Converting ${decimal} to its simplest fraction form ${fraction.display}"
        }
    }
    </script>

    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "HowTo",
        "name": "How to Convert ${decimal} to a Fraction",
        "description": "Step-by-step guide to convert ${decimal} decimal to fraction ${fraction.display}",
        "image": "https://asafractions.com/android-chrome-512x512.png",
        "totalTime": "PT2M",
        "estimatedCost": {
            "@type": "MonetaryAmount",
            "currency": "USD",
            "value": "0"
        },
        "supply": [
            {
                "@type": "HowToSupply",
                "name": "Calculator or pen and paper"
            }
        ],
        "step": [
            {
                "@type": "HowToStep",
                "name": "Write as fraction",
                "text": "Write ${decimal} as a fraction with appropriate denominator",
                "image": "https://asafractions.com/android-chrome-512x512.png"
            },
            {
                "@type": "HowToStep", 
                "name": "Simplify the fraction",
                "text": "Reduce the fraction to its simplest form: ${fraction.display}",
                "image": "https://asafractions.com/android-chrome-512x512.png"
            }
        ]
    }
    </script>

    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://asafractions.com/"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Fraction Converter",
                "item": "https://asafractions.com/"
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": "${decimal} as a Fraction",
                "item": "https://asafractions.com/${decimal}-as-a-fraction.html"
            }
        ]
    }
    </script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- SEO Enhancement Script -->
    <script src="seo-enhancements.js"></script>
    
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-0XHBW2YCCC"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-0XHBW2YCCC');
    </script>
</head>
<body>
    <!-- Content will be added here -->
    <!-- This is just the head section template -->
</body>
</html>`;
}

// Function to update existing fraction pages
function updateFractionPages() {
    const currentDir = process.cwd();
    const files = fs.readdirSync(currentDir);
    
    // Find all fraction pages
    const fractionPages = files.filter(file => 
        file.includes('-as-a-fraction.html') && 
        file !== 'fraction-page-template.html'
    );
    
    console.log(`Found ${fractionPages.length} fraction pages to update:`);
    fractionPages.forEach(page => console.log(`- ${page}`));
    
    // Update each page
    fractionPages.forEach(filename => {
        try {
            // Extract decimal from filename
            const decimal = filename.replace('-as-a-fraction.html', '');
            const fraction = decimalToFraction(decimal);
            
            console.log(`Updating ${filename} - ${decimal} = ${fraction.display}`);
            
            // Read existing file
            const filePath = path.join(currentDir, filename);
            let content = fs.readFileSync(filePath, 'utf8');
            
            // Update meta description
            content = content.replace(
                /<meta name="description" content="[^"]*">/,
                `<meta name="description" content="Convert ${decimal} to a fraction: ${decimal} = ${fraction.display}. Learn step-by-step how to convert ${decimal} decimal to fraction with our free calculator and comprehensive guide.">`
            );
            
            // Update title
            content = content.replace(
                /<title>[^<]*<\/title>/,
                `<title>${decimal} as a Fraction - Convert ${decimal} to Fraction (${fraction.display}) | Step-by-Step Guide</title>`
            );
            
            // Add canonical if not exists
            if (!content.includes('rel="canonical"')) {
                content = content.replace(
                    /<title>[^<]*<\/title>/,
                    `<title>${decimal} as a Fraction - Convert ${decimal} to Fraction (${fraction.display}) | Step-by-Step Guide</title>\n    \n    <!-- Canonical URL -->\n    <link rel="canonical" href="https://asafractions.com/${decimal}-as-a-fraction.html">`
                );
            }
            
            // Write updated content back
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✓ Updated ${filename}`);
            
        } catch (error) {
            console.error(`✗ Error updating ${filename}:`, error.message);
        }
    });
    
    console.log('\nSEO updates completed!');
}

// Export functions for use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        decimalToFraction,
        generateFractionPageHTML,
        updateFractionPages
    };
}

// Run if called directly
if (require.main === module) {
    updateFractionPages();
}

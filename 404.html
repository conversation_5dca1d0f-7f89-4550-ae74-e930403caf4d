<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Page not found - As A Fractions. The page you're looking for doesn't exist. Find decimal to fraction conversions and math tools on our homepage.">
    <meta name="keywords" content="404, page not found, decimal to fraction converter, math tools">
    <meta name="author" content="As A Fractions">
    <meta name="robots" content="noindex, nofollow">
    <title>Page Not Found (404) - As A Fractions | Decimal to Fraction Converter</title>
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Page Not Found - As A Fractions">
    <meta property="og:description" content="The page you're looking for doesn't exist. Find decimal to fraction conversions and math tools on our homepage.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://asafractions.com/404.html">
    <meta property="og:image" content="https://asafractions.com/android-chrome-512x512.png">
    <meta property="og:site_name" content="As A Fractions">
    
    <!-- Bootstrap CSS -->
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    
    <style>
        .error-page {
            min-height: 80vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .error-content {
            background: rgba(255,255,255,0.1);
            padding: 3rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .error-code {
            font-size: 8rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .error-message {
            font-size: 1.5rem;
            margin-bottom: 2rem;
        }
        .btn-home {
            background: #007bff;
            border: none;
            padding: 12px 30px;
            font-size: 1.1rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .suggestions {
            background: white;
            color: #333;
            padding: 2rem;
            border-radius: 10px;
            margin-top: 2rem;
        }
        .suggestion-item {
            display: inline-block;
            margin: 0.5rem;
            padding: 0.5rem 1rem;
            background: #f8f9fa;
            border-radius: 20px;
            text-decoration: none;
            color: #007bff;
            transition: all 0.3s ease;
        }
        .suggestion-item:hover {
            background: #007bff;
            color: white;
            text-decoration: none;
        }
    </style>
    
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-0XHBW2YCCC"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-0XHBW2YCCC');
        
        // Track 404 errors
        gtag('event', 'page_view', {
            'page_title': '404 Error',
            'page_location': window.location.href
        });
    </script>
</head>
<body>
    <!-- Header -->
    <header class="site-header">
        <div class="container">
            <nav class="navbar navbar-expand-lg navbar-light">
                <a class="navbar-brand" href="/">As A Fractions</a>
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/decimals-to-fractions">Convert Decimals Manually</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/measurement-converter">Measurement Converter</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/about-as-a-fractions">About</a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
    </header>

    <!-- Error Page Content -->
    <section class="error-page">
        <div class="container">
            <div class="error-content">
                <div class="error-code">404</div>
                <h1 class="error-message">Oops! Page Not Found</h1>
                <p class="lead">The page you're looking for seems to have vanished into thin air, just like a decimal without its fraction!</p>
                <a href="/" class="btn btn-primary btn-home">Return to Homepage</a>
                
                <div class="suggestions">
                    <h3>Looking for something specific? Try these popular conversions:</h3>
                    <a href="/0.5-as-a-fraction" class="suggestion-item">0.5 as a fraction</a>
                    <a href="/0.25-as-a-fraction" class="suggestion-item">0.25 as a fraction</a>
                    <a href="/0.75-as-a-fraction" class="suggestion-item">0.75 as a fraction</a>
                    <a href="/0.125-as-a-fraction" class="suggestion-item">0.125 as a fraction</a>
                    <a href="/0.375-as-a-fraction" class="suggestion-item">0.375 as a fraction</a>
                    <a href="/0.625-as-a-fraction" class="suggestion-item">0.625 as a fraction</a>
                    <a href="/fractions-table" class="suggestion-item">Fractions Table</a>
                    <a href="/decimals-to-fractions" class="suggestion-item">Manual Conversion Guide</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>About Us</h5>
                    <p>As A Fractions offers high-quality tools and services to help you with conversions, calculations, and more.</p>
                </div>
                <div class="col-md-4">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="/about-as-a-fractions">About</a></li>
                        <li><a href="/privacy-policy">Privacy Policy</a></li>
                        <li><a href="/terms-of-use">Terms of Use</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Useful Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="/decimals-to-fractions">Convert Decimals Manually</a></li>
                        <li><a href="/fractions-table">Fractions Table</a></li>
                        <li><a href="/measurement-converter">Measurement Converter</a></li>
                    </ul>
                </div>
            </div>
            <div class="text-center mt-4">
                <p>&copy; 2024 As A Fractions. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    
    <script>
        // Auto-redirect suggestions based on URL
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            
            // Check if URL looks like a decimal conversion
            const decimalMatch = currentPath.match(/(\d+\.?\d*)/);
            if (decimalMatch) {
                const decimal = decimalMatch[1];
                const suggestion = document.createElement('div');
                suggestion.className = 'alert alert-info mt-3';
                suggestion.innerHTML = `
                    <strong>Did you mean:</strong> 
                    <a href="/${decimal}-as-a-fraction" class="alert-link">${decimal} as a fraction</a>?
                `;
                document.querySelector('.suggestions').appendChild(suggestion);
            }
        });
    </script>
</body>
</html>

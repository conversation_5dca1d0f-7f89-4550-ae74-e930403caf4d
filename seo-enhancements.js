/**
 * SEO Enhancements Script for As A Fractions
 * Automatically adds canonical URLs and other SEO improvements
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add canonical URL if not already present
    addCanonicalURL();
    
    // Add structured data for fraction pages
    addFractionPageSchema();
    
    // Enhance meta descriptions dynamically
    enhanceMetaDescriptions();
    
    // Add breadcrumb schema
    addBreadcrumbSchema();
});

/**
 * Add canonical URL to pages that don't have one
 */
function addCanonicalURL() {
    const existingCanonical = document.querySelector('link[rel="canonical"]');
    if (!existingCanonical) {
        const canonical = document.createElement('link');
        canonical.rel = 'canonical';
        canonical.href = window.location.href.split('?')[0]; // Remove query parameters
        document.head.appendChild(canonical);
    }
}

/**
 * Add structured data for fraction conversion pages
 */
function addFractionPageSchema() {
    const pageTitle = document.title;
    const currentURL = window.location.href;
    
    // Check if this is a fraction page (contains "as-a-fraction" in URL)
    if (currentURL.includes('as-a-fraction') && !document.querySelector('script[type="application/ld+json"]')) {
        const decimal = extractDecimalFromURL(currentURL);
        if (decimal) {
            const fraction = convertDecimalToFraction(decimal);
            addFractionSchema(decimal, fraction, currentURL);
        }
    }
}

/**
 * Extract decimal value from URL
 */
function extractDecimalFromURL(url) {
    const match = url.match(/(\d+\.?\d*)-as-a-fraction/);
    return match ? match[1] : null;
}

/**
 * Convert decimal to fraction (simplified version)
 */
function convertDecimalToFraction(decimal) {
    const num = parseFloat(decimal);
    if (isNaN(num)) return null;
    
    // Simple conversion logic
    const denominator = Math.pow(10, decimal.split('.')[1]?.length || 0);
    const numerator = num * denominator;
    const gcd = findGCD(numerator, denominator);
    
    return {
        numerator: numerator / gcd,
        denominator: denominator / gcd,
        display: `${numerator / gcd}/${denominator / gcd}`
    };
}

/**
 * Find Greatest Common Divisor
 */
function findGCD(a, b) {
    return b === 0 ? a : findGCD(b, a % b);
}

/**
 * Add fraction-specific schema markup
 */
function addFractionSchema(decimal, fraction, url) {
    const schema = {
        "@context": "https://schema.org",
        "@type": "Article",
        "headline": `${decimal} as a Fraction - Convert ${decimal} to Fraction`,
        "description": `Convert ${decimal} to a fraction: ${decimal} = ${fraction.display}. Learn step-by-step how to convert ${decimal} decimal to fraction.`,
        "author": {
            "@type": "Organization",
            "name": "As A Fractions",
            "url": "https://asafractions.com/"
        },
        "publisher": {
            "@type": "Organization",
            "name": "As A Fractions",
            "logo": {
                "@type": "ImageObject",
                "url": "https://asafractions.com/android-chrome-512x512.png"
            }
        },
        "datePublished": "2024-01-01",
        "dateModified": new Date().toISOString().split('T')[0],
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": url
        },
        "image": "https://asafractions.com/android-chrome-512x512.png",
        "articleSection": "Mathematics",
        "keywords": [`${decimal} as a fraction`, `${decimal} to fraction`, `convert ${decimal} to fraction`],
        "about": {
            "@type": "Thing",
            "name": "Decimal to Fraction Conversion",
            "description": `Converting ${decimal} to its fraction equivalent ${fraction.display}`
        }
    };
    
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(schema, null, 2);
    document.head.appendChild(script);
}

/**
 * Enhance meta descriptions for better SEO
 */
function enhanceMetaDescriptions() {
    const metaDescription = document.querySelector('meta[name="description"]');
    const currentURL = window.location.href;
    
    if (currentURL.includes('as-a-fraction') && metaDescription) {
        const decimal = extractDecimalFromURL(currentURL);
        if (decimal) {
            const fraction = convertDecimalToFraction(decimal);
            if (fraction) {
                const enhancedDescription = `Convert ${decimal} to a fraction: ${decimal} = ${fraction.display}. Free online calculator with step-by-step guide. Perfect for students, teachers, and professionals learning fraction conversion.`;
                metaDescription.setAttribute('content', enhancedDescription);
            }
        }
    }
}

/**
 * Add breadcrumb schema markup
 */
function addBreadcrumbSchema() {
    const currentURL = window.location.href;
    const pathname = window.location.pathname;
    
    let breadcrumbList = [
        {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": "https://asafractions.com/"
        }
    ];
    
    // Add specific breadcrumbs based on page type
    if (pathname.includes('about')) {
        breadcrumbList.push({
            "@type": "ListItem",
            "position": 2,
            "name": "About",
            "item": currentURL
        });
    } else if (pathname.includes('as-a-fraction')) {
        const decimal = extractDecimalFromURL(currentURL);
        breadcrumbList.push({
            "@type": "ListItem",
            "position": 2,
            "name": "Fraction Converter",
            "item": "https://asafractions.com/"
        });
        if (decimal) {
            breadcrumbList.push({
                "@type": "ListItem",
                "position": 3,
                "name": `${decimal} as a Fraction`,
                "item": currentURL
            });
        }
    } else if (pathname.includes('decimals-to-fractions')) {
        breadcrumbList.push({
            "@type": "ListItem",
            "position": 2,
            "name": "Convert Decimals Manually",
            "item": currentURL
        });
    } else if (pathname.includes('fractions-table')) {
        breadcrumbList.push({
            "@type": "ListItem",
            "position": 2,
            "name": "Fractions Table",
            "item": currentURL
        });
    }
    
    const breadcrumbSchema = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": breadcrumbList
    };
    
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(breadcrumbSchema, null, 2);
    document.head.appendChild(script);
}

/**
 * Add Open Graph and Twitter Card meta tags dynamically
 */
function addSocialMetaTags() {
    const currentURL = window.location.href;
    const pageTitle = document.title;
    const metaDescription = document.querySelector('meta[name="description"]');
    const description = metaDescription ? metaDescription.getAttribute('content') : '';
    
    // Add Open Graph tags if not present
    if (!document.querySelector('meta[property="og:title"]')) {
        addMetaTag('property', 'og:title', pageTitle);
        addMetaTag('property', 'og:description', description);
        addMetaTag('property', 'og:url', currentURL);
        addMetaTag('property', 'og:type', 'website');
        addMetaTag('property', 'og:image', 'https://asafractions.com/android-chrome-512x512.png');
        addMetaTag('property', 'og:site_name', 'As A Fractions');
    }
    
    // Add Twitter Card tags if not present
    if (!document.querySelector('meta[name="twitter:card"]')) {
        addMetaTag('name', 'twitter:card', 'summary_large_image');
        addMetaTag('name', 'twitter:title', pageTitle);
        addMetaTag('name', 'twitter:description', description);
        addMetaTag('name', 'twitter:image', 'https://asafractions.com/android-chrome-512x512.png');
    }
}

/**
 * Helper function to add meta tags
 */
function addMetaTag(attribute, name, content) {
    const meta = document.createElement('meta');
    meta.setAttribute(attribute, name);
    meta.setAttribute('content', content);
    document.head.appendChild(meta);
}

// Initialize social meta tags
addSocialMetaTags();
